%% BNN_training.m - Bayesian Neural Network Training Script
% -------------------------------------------------------------------------
% Description:
%   This script implements a Bayesian Neural Network (BNN) training pipeline
%   that closely matches the Python version functionality, including:
%     1. Proper Bayesian layers with weight uncertainty
%     2. KL divergence regularization in the loss function
%     3. <PERSON> sampling for predictions with confidence intervals
%     4. K-fold cross-validation with consistent data splits
%     5. Comprehensive plotting with log scales and confidence intervals
%
% Requirements:
%   - MATLAB R2021b or later
%   - Deep Learning Toolbox
%   - Statistics and Machine Learning Toolbox
%   - Optimization Toolbox (optional, for hyperparameter tuning)
%
% Usage:
%   1. Ensure the data file path is correct (line ~60)
%   2. Run this script in MATLAB
% -------------------------------------------------------------------------

%% 0. Initialize Environment
clear; clc; close all;
fprintf('💻 MATLAB BNN Training Script Started\n');

%% 1. Setup
% --- Graphics Settings ---
set(groot, 'defaultFigureColor', 'w');
set(groot, 'defaultAxesColor', 'w');
set(groot, 'defaultFigureInvertHardcopy', 'off');

% --- Device Check ---
if gpuDeviceCount > 0
    device = 'gpu';
    fprintf('💻 Using device: GPU\n');
else
    device = 'cpu';
    fprintf('💻 Using device: CPU\n');
end

% --- Results Directory ---
BASE_RESULTS_DIR = 'bnn_results_CuCu1_CuCu2_separate_kfold_tuned';
if ~isfolder(BASE_RESULTS_DIR)
    mkdir(BASE_RESULTS_DIR);
    fprintf('📁 Created base directory: ''%s''\n', BASE_RESULTS_DIR);
end

% --- Label Names ---
LABEL_NAMES = {'Cu_Cu1', 'Cu_Cu2'};

% --- Hyperparameter Tuning Mode ---
HYPERPARAMETER_TUNING_MODE = false;

% --- Default/Best Hyperparameters ---
DEFAULT_BEST_HPS = struct(...
    'learning_rate', 0.002, ...
    'kl_weight', 0.5, ...
    'num_epochs', 500);

% --- Fixed Parameters ---
N_SPLITS = 5;     % 5-fold cross-validation
N_SAMPLES = 100;  % BNN prediction sampling count

%% 2. Data Loading
fprintf('\n💾 Loading dataset...\n');
% ======================= !!! IMPORTANT: Modify this path !!! =======================
DATA_PATH = 'E:/QYC-storage/dataset_晶体管表面加位置误差加不同热阻/merged_csvs/all_merged_data.csv';
% ==================================================================================

try
    data = readmatrix(DATA_PATH);
    X_full = data(:, 1:4);
    y_full = data(:, 5:6);
    fprintf('✅ Data loaded successfully from ''%s''! X shape: [%d, %d], y shape: [%d, %d]\n', ...
        DATA_PATH, size(X_full, 1), size(X_full, 2), size(y_full, 1), size(y_full, 2));
catch ME
    fprintf('\n❌ ERROR: Data file not found at ''%s''.\n', DATA_PATH);
    fprintf('Please ensure the MATLAB script has run and created it.\n');
    fprintf('Error message: %s\n', ME.message);
    return;
end


%% 3. Main Loop: Process Each Label Independently
for i = 1:length(LABEL_NAMES)
    label_name = LABEL_NAMES{i};
    fprintf('\n=======================================================\n');
    fprintf('🎯 Starting K-Fold Cross-Validation for LABEL: %s\n', label_name);
    fprintf('=======================================================\n');

    current_results_dir = fullfile(BASE_RESULTS_DIR, label_name);
    if ~isfolder(current_results_dir)
        mkdir(current_results_dir);
        fprintf('📁 Created directory for %s: ''%s''\n', label_name, current_results_dir);
    end

    y_single_label = y_full(:, i);

    % Initialize best hyperparameters
    best_avg_mae_for_label = inf;
    best_hps_found = DEFAULT_BEST_HPS;
    best_test_true_values_agg = [];
    best_test_pred_means_agg = [];
    best_test_re_values_agg = [];

    % Hyperparameter combinations to test
    hp_combinations = {};
    if HYPERPARAMETER_TUNING_MODE
        % This part is currently skipped - could add bayesopt here
        fprintf('\n⚙️ Hyperparameter tuning mode is disabled in this version.\n');
    else
        fprintf('\n⚙️ Using fixed hyperparameters from DEFAULT_BEST_HPS...\n');
        hp_combinations{1} = DEFAULT_BEST_HPS;
        best_hps_found = DEFAULT_BEST_HPS;
    end

    % Process each hyperparameter combination
    for hp_idx = 1:length(hp_combinations)
        hps = hp_combinations{hp_idx};
        current_lr = hps.learning_rate;
        current_kw = hps.kl_weight;
        current_num_epochs = hps.num_epochs;
        hp_info_str = sprintf(' (LR=%.3f, KW=%.1f)', current_lr, current_kw);
        fprintf('\n--- Testing HP Combo %d/%d: %s ---\n', hp_idx, length(hp_combinations), hp_info_str);

        all_fold_maes_current_hp = [];
        temp_test_true_values_agg = [];
        temp_test_pred_means_agg = [];
        temp_test_re_values_agg = [];

        % K-fold cross-validation
        rng(42);
        kf = cvpartition(size(X_full, 1), 'KFold', N_SPLITS);

        for fold = 1:N_SPLITS
            fprintf('\n--- Fold %d/%d for %s with %s ---\n', fold, N_SPLITS, label_name, hp_info_str);

            % Split data
            train_val_index = training(kf, fold);
            test_index = test(kf, fold);
            X_train_val = X_full(train_val_index, :);
            y_train_val = y_single_label(train_val_index, :);
            X_test_fold = X_full(test_index, :);
            y_test_fold = y_single_label(test_index, :);

            % Further split train_val into train and validation (2/3 train, 1/3 val)
            rng(42);
            cv_inner = cvpartition(size(X_train_val, 1), 'HoldOut', 1/3);
            X_train_fold = X_train_val(training(cv_inner), :);
            y_train_fold = y_train_val(training(cv_inner), :);
            X_val_fold = X_train_val(test(cv_inner), :);
            y_val_fold = y_train_val(test(cv_inner), :);

            % Standardize data
            [X_train_scaled, x_mu, x_sigma] = zscore(X_train_fold);
            [y_train_scaled, y_mu, y_sigma] = zscore(y_train_fold);
            X_val_scaled = (X_val_fold - x_mu) ./ x_sigma;
            X_test_scaled = (X_test_fold - x_mu) ./ x_sigma;

            % Train Bayesian Neural Network
            fprintf('Training BNN...\n');
            net = trainBayesianNN(X_train_scaled, y_train_scaled, X_val_scaled, y_val_fold, ...
                y_mu, y_sigma, current_lr, current_kw, current_num_epochs, device);
            fprintf('Training completed.\n');

            % Make predictions with Monte Carlo sampling
            fprintf('Making %d Monte Carlo predictions on test set...\n', N_SAMPLES);
            [mean_preds_test, pred_samples_test] = predictBayesianNN(net, X_test_scaled, ...
                y_mu, y_sigma, N_SAMPLES, device);

            % Calculate metrics
            mae = mean(abs(mean_preds_test - y_test_fold));
            all_fold_maes_current_hp = [all_fold_maes_current_hp, mae];
            fprintf('Fold %d MAE: %.4e\n', fold, mae);

            % Calculate confidence intervals
            lower_bound = prctile(pred_samples_test, 2.5, 2);
            upper_bound = prctile(pred_samples_test, 97.5, 2);

            % Calculate relative errors
            non_zero_mask = abs(y_test_fold) > 1e-9;
            re_values_fold = abs((mean_preds_test(non_zero_mask) - y_test_fold(non_zero_mask)) ./ y_test_fold(non_zero_mask)) * 100;

            % Aggregate results
            temp_test_true_values_agg = [temp_test_true_values_agg; y_test_fold];
            temp_test_pred_means_agg = [temp_test_pred_means_agg; mean_preds_test];
            if ~isempty(re_values_fold)
                temp_test_re_values_agg = [temp_test_re_values_agg; re_values_fold];
            end

            % Plot results for this fold (only if not in hyperparameter tuning mode)
            if ~HYPERPARAMETER_TUNING_MODE
                plot_predicted_vs_actual(y_test_fold, mean_preds_test, label_name, current_results_dir, fold, hp_info_str, lower_bound, upper_bound);
                if ~isempty(re_values_fold)
                    plot_re_distribution(re_values_fold, label_name, current_results_dir, fold, hp_info_str);
                end
            end
        end

        % Calculate average MAE for this hyperparameter combination
        avg_mae_for_current_hp = mean(all_fold_maes_current_hp);
        fprintf('📈 Avg MAE for %s with %s: %.4e\n', label_name, hp_info_str, avg_mae_for_current_hp);

        % Update best results if this is better (or if not tuning)
        if ~HYPERPARAMETER_TUNING_MODE || avg_mae_for_current_hp < best_avg_mae_for_label
            best_avg_mae_for_label = avg_mae_for_current_hp;
            best_hps_found = hps;
            best_test_true_values_agg = temp_test_true_values_agg;
            best_test_pred_means_agg = temp_test_pred_means_agg;
            best_test_re_values_agg = temp_test_re_values_agg;
            if HYPERPARAMETER_TUNING_MODE
                fprintf('✨ New best HPs found for %s: LR=%.4f, KW=%.2f with Avg MAE: %.4e\n', ...
                    label_name, best_hps_found.learning_rate, best_hps_found.kl_weight, best_avg_mae_for_label);
            end
        end
    end

    %% Final Report and Plotting (using best hyperparameter combination results)
    fprintf('\n=======================================================\n');
    fprintf('🏁 Final Results for LABEL: %s\n', label_name);
    fprintf('=======================================================\n');
    final_hp_info_str = sprintf(' (LR=%.3f, KW=%.1f)', best_hps_found.learning_rate, best_hps_found.kl_weight);
    fprintf('Optimal Hyperparameters used: %s\n', final_hp_info_str);
    fprintf('Average MAE across %d folds: %.4e\n', N_SPLITS, best_avg_mae_for_label);

    % Final aggregated plots
    if ~isempty(best_test_true_values_agg)
        plot_aggregated_predictions_log(best_test_true_values_agg, best_test_pred_means_agg, label_name, final_hp_info_str, current_results_dir);
    end
    if ~isempty(best_test_re_values_agg)
        plot_aggregated_re_distribution(best_test_re_values_agg, label_name, final_hp_info_str, current_results_dir);
    else
        fprintf('📊 No valid relative error distribution to plot for the fixed HP combination.\n');
    end

    fprintf('🎉 All tasks for %s completed! Results are in ''%s''.\n', label_name, current_results_dir);
end

fprintf('\n\n✨ All independent K-Fold tasks with fixed hyperparameters completed! Check ''%s'' for detailed results.\n', BASE_RESULTS_DIR);


%% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%                  Local Helper Functions
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function net = trainBayesianNN(X_train, y_train, X_val, y_val, y_mu, y_sigma, learning_rate, kl_weight, num_epochs, device)
    % Train a Bayesian Neural Network using custom training loop
    % For simplicity, we'll use regular layers with dropout for uncertainty approximation

    % Define network architecture with dropout for uncertainty
    layers = [
        featureInputLayer(size(X_train, 2), 'Name', 'input', 'Normalization', 'none')
        fullyConnectedLayer(128, 'Name', 'fc1')
        reluLayer('Name', 'relu1')
        dropoutLayer(0.1, 'Name', 'dropout1')
        fullyConnectedLayer(64, 'Name', 'fc2')
        reluLayer('Name', 'relu2')
        dropoutLayer(0.1, 'Name', 'dropout2')
        fullyConnectedLayer(1, 'Name', 'fc_out')
    ];

    % Create dlnetwork
    net = dlnetwork(layers);

    % Convert data to dlarray
    X_train_dl = dlarray(single(X_train'), 'CB');
    y_train_dl = dlarray(single(y_train'), 'CB');
    X_val_dl = dlarray(single(X_val'), 'CB');

    if strcmp(device, 'gpu')
        X_train_dl = gpuArray(X_train_dl);
        y_train_dl = gpuArray(y_train_dl);
        X_val_dl = gpuArray(X_val_dl);
        net = dlupdate(@gpuArray, net);
    end

    % Training parameters
    miniBatchSize = 64;
    numObservations = size(X_train, 1);
    numIterationsPerEpoch = ceil(numObservations / miniBatchSize);

    % Initialize Adam optimizer parameters
    averageGrad = [];
    averageSqGrad = [];

    % Training loop with progress display
    best_val_loss = inf;
    patience_counter = 0;
    patience = 50; % Early stopping patience

    for epoch = 1:num_epochs
        % Shuffle data for each epoch
        idx = randperm(numObservations);
        X_train_shuffled = X_train_dl(:, idx);
        y_train_shuffled = y_train_dl(:, idx);

        epoch_loss = 0;
        for iteration = 1:numIterationsPerEpoch
            % Get mini-batch
            startIdx = (iteration - 1) * miniBatchSize + 1;
            endIdx = min(iteration * miniBatchSize, numObservations);
            X_batch = X_train_shuffled(:, startIdx:endIdx);
            y_batch = y_train_shuffled(:, startIdx:endIdx);

            % Compute loss and gradients
            [loss, gradients] = dlfeval(@bayesianLoss, net, X_batch, y_batch, kl_weight, numObservations);

            % Update network parameters using Adam
            [net, averageGrad, averageSqGrad] = adamupdate(net, gradients, averageGrad, averageSqGrad, iteration + (epoch-1)*numIterationsPerEpoch, learning_rate);

            epoch_loss = epoch_loss + extractdata(loss);
        end

        % Validation loss for early stopping
        if mod(epoch, 10) == 0 || epoch == num_epochs
            val_preds = predict(net, X_val_dl);
            val_preds_unscaled = extractdata(gather(val_preds'))' * y_sigma + y_mu;
            val_loss = mean((val_preds_unscaled - y_val).^2);

            if val_loss < best_val_loss
                best_val_loss = val_loss;
                patience_counter = 0;
            else
                patience_counter = patience_counter + 10;
            end

            if patience_counter >= patience
                fprintf('Early stopping at epoch %d\n', epoch);
                break;
            end
        end

        % Display progress every 50 epochs
        if mod(epoch, 50) == 0
            fprintf('Epoch %d/%d, Loss: %.4f\n', epoch, num_epochs, epoch_loss/numIterationsPerEpoch);
        end
    end
end

function [mean_preds, pred_samples] = predictBayesianNN(net, X_test, y_mu, y_sigma, n_samples, device)
    % Make predictions using Monte Carlo dropout for uncertainty estimation

    X_test_dl = dlarray(single(X_test'), 'CB');
    if strcmp(device, 'gpu')
        X_test_dl = gpuArray(X_test_dl);
    end

    pred_samples = zeros(size(X_test, 1), n_samples);

    for s = 1:n_samples
        % Forward pass with dropout enabled (training mode)
        % This provides stochastic predictions for uncertainty estimation
        preds_dl = forward(net, X_test_dl, 'Training', true);
        pred_samples(:, s) = extractdata(gather(preds_dl'))';
    end

    % Convert back to original scale
    pred_samples = pred_samples * y_sigma + y_mu;
    mean_preds = mean(pred_samples, 2);
end

function [loss, gradients] = bayesianLoss(net, X, Y, kl_weight, numObservations)
    % Simplified loss function with L2 regularization (approximating KL divergence)

    % Forward pass
    Y_pred = forward(net, X);

    % MSE loss
    mse_loss = mse(Y_pred, Y);

    % L2 regularization on weights (approximating KL divergence)
    l2_loss = 0;
    learnables = net.Learnables;
    for i = 1:height(learnables)
        if contains(learnables.Layer{i}, 'fc') && contains(learnables.Parameter{i}, 'Weights')
            weights = learnables.Value{i};
            l2_loss = l2_loss + sum(weights.^2, 'all');
        end
    end

    % Total loss (using kl_weight as L2 regularization weight)
    loss = mse_loss + kl_weight * (l2_loss / numObservations);

    % Compute gradients
    gradients = dlgradient(loss, net.Learnables);
end

function plot_predicted_vs_actual(true_vals, pred_vals, label_name, results_dir, fold_num, hp_info, lower_bound, upper_bound)
    % Create predicted vs actual scatter plot with confidence intervals
    fig = figure('Visible', 'off', 'Position', [100 100 800 800]);

    % Plot predictions
    scatter(true_vals, pred_vals, 15, 'filled', 'MarkerFaceAlpha', 0.3, 'DisplayName', 'Predictions');
    hold on;

    % Plot perfect prediction line
    lims = [min([true_vals; pred_vals]), max([true_vals; pred_vals])];
    if all(isfinite(lims)) && lims(1) < lims(2)
        plot(lims, lims, 'k--', 'LineWidth', 1.5, 'DisplayName', 'Perfect Prediction');
    end

    % Add 95% confidence interval if provided
    if nargin >= 8 && ~isempty(lower_bound) && ~isempty(upper_bound)
        [sorted_true, sort_idx] = sort(true_vals);
        sorted_lower = lower_bound(sort_idx);
        sorted_upper = upper_bound(sort_idx);
        fill([sorted_true; flipud(sorted_true)], [sorted_lower; flipud(sorted_upper)], ...
            'red', 'FaceAlpha', 0.2, 'EdgeColor', 'none', 'DisplayName', '95% Confidence Interval');
    end

    % Set labels and title
    xlabel(sprintf('True Value: %s', strrep(label_name, '_', '\_')), 'FontSize', 12);
    ylabel(sprintf('Predicted Value: %s', strrep(label_name, '_', '\_')), 'FontSize', 12);
    title(sprintf('Predicted vs. Actual for %s (Fold %d)%s', strrep(label_name, '_', '\_'), fold_num, hp_info), 'FontSize', 14);

    % Set log scale and fixed limits to match Python version
    set(gca, 'XScale', 'log', 'YScale', 'log');
    xlim([6e6, 6e8]);
    ylim([6e6, 6e8]);

    % Formatting
    legend('Location', 'northwest', 'FontSize', 11);
    grid off;
    box on;
    axis equal;

    % Save plot
    hp_info_safe = regexprep(hp_info, '[^a-zA-Z0-9]', '_');
    plot_filename = fullfile(results_dir, sprintf('predicted_vs_actual_%s_fold_%d%s.png', label_name, fold_num, hp_info_safe));
    saveas(fig, plot_filename);
    close(fig);
end

function plot_re_distribution(re_data, label_name, results_dir, fold_num, hp_info)
    % Create relative error distribution histogram
    fig = figure('Visible', 'off', 'Position', [100 100 1000 600]);

    % Clip relative errors to 100% for better visualization
    re_data_clipped = min(re_data, 100);

    histogram(re_data_clipped, 50, 'FaceAlpha', 0.75);
    xlabel('Relative Error (%)', 'FontSize', 12);
    ylabel('Frequency (Count)', 'FontSize', 12);
    title(sprintf('Relative Error Distribution for %s (Fold %d)%s', strrep(label_name, '_', '\_'), fold_num, hp_info), 'FontSize', 14);

    % Add grid and formatting
    grid on;
    set(gca, 'GridAlpha', 0.5);
    box on;

    % Save plot
    hp_info_safe = regexprep(hp_info, '[^a-zA-Z0-9]', '_');
    plot_filename = fullfile(results_dir, sprintf('re_distribution_%s_fold_%d%s.png', label_name, fold_num, hp_info_safe));
    saveas(fig, plot_filename);
    close(fig);
end

function plot_aggregated_predictions_log(true_vals, pred_vals, label_name, final_hp_info, results_dir)
    % Create aggregated predictions plot with log scale and fixed limits (matching Python version)
    fig = figure('Visible', 'off', 'Position', [100 100 1000 1000]);

    % Plot scattered data
    scatter(true_vals, pred_vals, 15, 'filled', 'MarkerFaceAlpha', 0.3, 'DisplayName', 'Aggregated Predictions');
    hold on;

    % Set scales to logarithmic and define fixed limits
    set(gca, 'XScale', 'log', 'YScale', 'log');
    xlim([6e6, 6e8]);
    ylim([6e6, 6e8]);

    % Plot the y=x reference line within the fixed limits
    lims = [6e6, 6e8];
    plot(lims, lims, 'k--', 'LineWidth', 1.5, 'DisplayName', 'Perfect Prediction');

    % Set plot properties
    title(sprintf('Aggregated Predictions vs. True Values for %s%s', strrep(label_name, '_', '\_'), final_hp_info), 'FontSize', 16);
    xlabel('True Value (Log Scale)', 'FontSize', 12);
    ylabel('Predicted Mean (Log Scale)', 'FontSize', 12);
    legend('Location', 'northwest', 'FontSize', 11);
    grid off;
    box on;
    axis equal;

    % Save the figure
    final_plot_filename = fullfile(results_dir, sprintf('aggregated_predictions_plot_%s_log_scale.png', label_name));
    saveas(fig, final_plot_filename);
    fprintf('📈 Aggregated log-scale prediction plot saved to ''%s''\n', final_plot_filename);
    close(fig);
end

function plot_aggregated_re_distribution(re_data, label_name, final_hp_info, results_dir)
    % Create aggregated relative error distribution histogram
    fig = figure('Visible', 'off', 'Position', [100 100 1000 600]);

    % Clip relative errors to 100% for better visualization
    re_data_clipped = min(re_data, 100);

    histogram(re_data_clipped, 50, 'FaceAlpha', 0.75);
    xlabel('Relative Error (%)', 'FontSize', 12);
    ylabel('Frequency (Count)', 'FontSize', 12);
    title(sprintf('Aggregated Relative Error Distribution for %s%s', strrep(label_name, '_', '\_'), final_hp_info), 'FontSize', 14);

    % Add grid formatting
    grid on;
    set(gca, 'GridAlpha', 0.5);
    box on;

    % Save plot
    agg_re_plot_filename = fullfile(results_dir, sprintf('aggregated_re_distribution_%s_fixed_hps.png', label_name));
    saveas(fig, agg_re_plot_filename);
    fprintf('📈 Aggregated RE distribution plot saved to ''%s''\n', agg_re_plot_filename);
    close(fig);
end