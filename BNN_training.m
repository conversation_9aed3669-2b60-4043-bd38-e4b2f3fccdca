%% run_bnn_training_optimized.m
% -------------------------------------------------------------------------
% 描述:
%   此优化后的单一文件脚本使用 MATLAB Deep Learning Toolbox 训练贝叶斯神经网络 (BNN)。
%   核心改进：
%     1. 使用贝叶斯优化 (bayesopt) 自动搜索最佳超参数（学习率和KL权重）。
%     2. 使用找到的最佳超参数，在完整的 K-折交叉验证中评估模型性能。
%     3. 采用 `dlTrainingLossParameter` 对象来设置KL权重，以确保与 
%        MATLAB R2021b/R2022a 版本的兼容性。
%
% 要求:
%   - MATLAB (R2021b, R2022a)
%   - Deep Learning Toolbox
%   - Statistics and Machine Learning Toolbox (用于 cvpartition)
%   - Optimization Toolbox (用于 bayesopt)
%
% 如何运行:
%   1. 将此文件的全部内容保存为 .m 文件。
%   2. 修改下面的 DATA_PATH 变量，使其指向您的数据文件。
%   3. 在 MATLAB 中运行此文件。
% -------------------------------------------------------------------------

%% 0. 初始化环境
clear; clc; close all;
fprintf('MATLAB BNN 自动超参数优化与训练脚本 (R2021b/R2022a 兼容版) 已启动。\n');

%% 1. 设置
% --- 图形设置 ---
set(groot, 'defaultFigureColor', 'w');
set(groot, 'defaultAxesColor', 'w');
set(groot, 'defaultFigureInvertHardcopy', 'off');

% --- 设备检查 ---
if gpuDeviceCount > 0
    device = "gpu";
    fprintf('💻 使用设备: GPU\n');
else
    device = "cpu";
    fprintf('💻 使用设备: CPU\n');
end

% --- 结果保存目录 ---
BASE_RESULTS_DIR = 'bnn_results_matlab_bayesopt_tuned';
if ~isfolder(BASE_RESULTS_DIR)
    mkdir(BASE_RESULTS_DIR);
    fprintf('📁 已创建基础目录: ''%s''\n', BASE_RESULTS_DIR);
end

% --- 标签名称 ---
LABEL_NAMES = {'Cu_Cu1', 'Cu_Cu2'};

% --- 固定参数 ---
NUM_EPOCHS = 500; % 训练周期固定
N_SPLITS = 5;     % 5-折交叉验证
N_SAMPLES = 100;  % BNN预测采样次数

%% 2. 数据加载
fprintf('\n💾 加载数据集...\n');
% ======================= !!! 重要: 请修改此路径 !!! =======================
DATA_PATH = 'E:/QYC-storage/dataset_晶体管表面加位置误差加不同热阻/merged_csvs/all_merged_data.csv';
% ========================================================================

try
    data = readmatrix(DATA_PATH);
    X_full = data(:, 1:4);
    y_full = data(:, 5:6);
    fprintf('✅ 数据已从 ''%s'' 成功加载！ X shape: [%d, %d], y 形状: [%d, %d]\n', ...
        DATA_PATH, size(X_full, 1), size(X_full, 2), size(y_full, 1), size(y_full, 2));
catch ME
    fprintf('\n❌ 错误: 在 ''%s'' 处找不到或无法读取数据文件。\n', DATA_PATH);
    fprintf('请确保第 49 行的 DATA_PATH 变量已正确设置。\n');
    fprintf('错误信息: %s\n', ME.message);
    return;
end


%% 3. 主循环：为每个标签独立进行优化和训练
for i = 1:length(LABEL_NAMES)
    label_name = LABEL_NAMES{i};
    fprintf('\n===========================================================\n');
    fprintf('🎯 开始处理标签: %s\n', label_name);
    fprintf('===========================================================\n');

    current_results_dir = fullfile(BASE_RESULTS_DIR, label_name);
    if ~isfolder(current_results_dir)
        mkdir(current_results_dir);
        fprintf('📁 已为 %s 创建目录: ''%s''\n', label_name, current_results_dir);
    end

    y_single_label = y_full(:, i);

    %% 4. 步骤一: 使用贝叶斯优化 (bayesopt) 自动寻找最佳超参数
    fprintf('\n⚙️ 步骤 1: 开始为 %s 进行自动超参数优化...\n', label_name);
    
    rng(42);
    cv_opt = cvpartition(size(X_full, 1), 'HoldOut', 0.2);
    X_train_opt = X_full(training(cv_opt), :);
    y_train_opt = y_single_label(training(cv_opt), :);
    X_val_opt = X_full(test(cv_opt), :);
    y_val_opt = y_single_label(test(cv_opt), :);
    
    [X_train_opt_scaled, x_mu_opt, x_sigma_opt] = zscore(X_train_opt);
    [y_train_opt_scaled, y_mu_opt, y_sigma_opt] = zscore(y_train_opt);
    X_val_opt_scaled = (X_val_opt - x_mu_opt) ./ x_sigma_opt;

    optimizableVars = [
        optimizableVariable('initialLearnRate', [1e-4, 1e-2], 'Transform', 'log');
        optimizableVariable('klWeight', [0.01, 1.0], 'Transform', 'log')
    ];

    objectiveFunction = makeObjectiveFunction(X_train_opt_scaled, y_train_opt_scaled, ...
        X_val_opt_scaled, y_val_opt, y_mu_opt, y_sigma_opt, NUM_EPOCHS, device);
    
    BayesoptResults = bayesopt(objectiveFunction, optimizableVars, ...
        'MaxObjectiveEvaluations', 20, ...
        'AcquisitionFunctionName', 'expected-improvement-plus', ...
        'Verbose', 1);

    best_hps_found = BayesoptResults.XAtMinObjective;
    fprintf('\n✨ 优化完成！为 %s 找到的最佳超参数:\n', label_name);
    fprintf('   - 最佳学习率 (InitialLearnRate): %.5f\n', best_hps_found.initialLearnRate);
    fprintf('   - 最佳 KL 权重 (klWeight): %.5f\n', best_hps_found.klWeight);

    %% 5. 步骤二: 使用找到的最佳超参数进行 K-折交叉验证
    fprintf('\n🏁 步骤 2: 使用最佳超参数对 %s 进行 %d-折交叉验证...\n', label_name, N_SPLITS);

    best_test_true_values_agg = [];
    best_test_pred_means_agg = [];
    best_test_re_values_agg = [];
    all_fold_maes = [];

    rng(42);
    kf = cvpartition(size(X_full, 1), 'KFold', N_SPLITS);
    
    hp_info_str = sprintf('(LR=%.4f, KW=%.2f)', best_hps_found.initialLearnRate, best_hps_found.klWeight);

    for fold = 1:N_SPLITS
        fprintf('\n--- Fold %d/%d ---\n', fold, N_SPLITS);

        train_idx = training(kf, fold);
        test_idx = test(kf, fold);
        X_train_fold = X_full(train_idx, :);
        y_train_fold = y_single_label(train_idx, :);
        X_test_fold = X_full(test_idx, :);
        y_test_fold = y_single_label(test_idx, :);

        [X_train_scaled, x_mu, x_sigma] = zscore(X_train_fold);
        [y_train_scaled, y_mu, y_sigma] = zscore(y_train_fold);
        X_test_scaled = (X_test_fold - x_mu) ./ x_sigma;
        
        options = trainingOptions('adam', ...
            'InitialLearnRate', best_hps_found.initialLearnRate, ...
            'MaxEpochs', NUM_EPOCHS, 'MiniBatchSize', 64, 'Shuffle', 'every-epoch', ...
            'ExecutionEnvironment', device, 'Verbose', false, 'Plots', 'none');

        fprintf('开始训练... \n');
        % *** 版本兼容性修正: 使用 dlTrainingLossParameter ***
        layers = defineBNNLayers(size(X_train_scaled, 2));
        dnet = dlnetwork(layers);
        lossParams = dlTrainingLossParameter('KLWeight', best_hps_found.klWeight);
        net = trainnet(X_train_scaled, y_train_scaled, dnet, @mseRegressionLoss, options, lossParams);
        fprintf('训练完成。\n');

        fprintf('在测试集上进行 %d 次采样预测...\n', N_SAMPLES);
        test_predictions_scaled = predict_bnn_ensemble(net, X_test_scaled, N_SAMPLES, device);
        
        mean_preds_scaled = mean(test_predictions_scaled, 2);
        mean_preds_test = mean_preds_scaled * y_sigma + y_mu;
        mae = mean(abs(mean_preds_test - y_test_fold));
        all_fold_maes = [all_fold_maes, mae];
        fprintf('Fold %d MAE: %.4e\n', fold, mae);

        non_zero_mask = abs(y_test_fold) > 1e-9;
        re_values_fold = abs((mean_preds_test(non_zero_mask) - y_test_fold(non_zero_mask)) ./ y_test_fold(non_zero_mask)) * 100;
        
        best_test_true_values_agg = [best_test_true_values_agg; y_test_fold];
        best_test_pred_means_agg = [best_test_pred_means_agg; mean_preds_test];
        best_test_re_values_agg = [best_test_re_values_agg; re_values_fold];
        
        plot_predicted_vs_actual(y_test_fold, mean_preds_test, label_name, current_results_dir, fold, hp_info_str);
        if ~isempty(re_values_fold)
            plot_re_distribution(re_values_fold, label_name, current_results_dir, fold, hp_info_str);
        end
    end

    %% 6. 最终报告和绘图
    avg_mae_final = mean(all_fold_maes);
    std_mae_final = std(all_fold_maes);

    fprintf('\n============================================================\n');
    fprintf('📊 标签: %s 的最终评估结果\n', label_name);
    fprintf('============================================================\n');
    fprintf('使用的最佳超参数: %s\n', hp_info_str);
    fprintf('在 %d 折交叉验证中的平均 MAE: %.4e (标准差: %.4e)\n', N_SPLITS, avg_mae_final, std_mae_final);

    if ~isempty(best_test_true_values_agg), plot_aggregated_predictions(best_test_true_values_agg, best_test_pred_means_agg, label_name, hp_info_str, current_results_dir); end
    if ~isempty(best_test_re_values_agg), plot_aggregated_re_distribution(best_test_re_values_agg, label_name, hp_info_str, current_results_dir); else, fprintf("📊 没有有效的聚合相对误差数据可供绘制。\n"); end
    
    fprintf('🎉 所有关于 %s 的任务已完成！结果保存在 ''%s''。\n', label_name, current_results_dir);
end

fprintf('\n\n✨ 所有标签的自动优化和交叉验证已全部完成！请查看 ''%s'' 获取详细结果。\n', BASE_RESULTS_DIR);


%% %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%                  本地辅助函数 (Local Helper Functions)
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

function obj = makeObjectiveFunction(X_train, y_train, X_val, y_val, y_mu, y_sigma, numEpochs, device)
    obj = @objective;
    function error = objective(optimizable_hps)
        options = trainingOptions('adam', ...
            'InitialLearnRate', optimizable_hps.initialLearnRate, ...
            'MaxEpochs', numEpochs, 'MiniBatchSize', 64, 'Shuffle', 'every-epoch', ...
            'ExecutionEnvironment', device, 'Verbose', false, 'Plots', 'none');

        % *** 版本兼容性修正: 使用 dlTrainingLossParameter ***
        layers = defineBNNLayers(size(X_train, 2));
        dnet = dlnetwork(layers);
        lossParams = dlTrainingLossParameter('KLWeight', optimizable_hps.klWeight);
        
        % 使用 lossParams 对象进行训练
        net = trainnet(X_train, y_train, dnet, @mseRegressionLoss, options, lossParams);

        val_preds_scaled = predict_bnn_ensemble(net, X_val, 50, device);
        mean_preds_scaled = mean(val_preds_scaled, 2);
        mean_preds_val = mean_preds_scaled * y_sigma + y_mu;
        error = mean(abs(mean_preds_val - y_val));
    end
end

function loss = mseRegressionLoss(Y, T)
    % 标准的均方误差损失函数
    loss = mse(Y, T);
end

function layers = defineBNNLayers(input_features)
    layers = [
        featureInputLayer(input_features, 'Name', 'input', 'Normalization', 'none')
        fullyConnectedLayer(128, 'Name', 'fc1')
        reluLayer('Name', 'relu1')
        fullyConnectedLayer(64, 'Name', 'fc2')
        reluLayer('Name', 'relu2')
        fullyConnectedLayer(1, 'Name', 'fc_out')
    ];
end

function predictions = predict_bnn_ensemble(net, X_data, n_samples, device)
    % 将输入数据转换为 dlarray
    X_data_dl = dlarray(single(X_data'), 'CB');
    if device == "gpu"
        X_data_dl = gpuArray(X_data_dl);
    end

    predictions = zeros(size(X_data, 1), n_samples);
    for s = 1:n_samples
        % 预测并转换回普通数组
        preds_dl = predict(net, X_data_dl);
        predictions(:, s) = extractdata(gather(preds_dl))';
    end
end

function plot_predicted_vs_actual(true_vals, pred_vals, label_name, results_dir, fold_num, hp_info)
    fig = figure('Visible', 'off');
    scatter(true_vals, pred_vals, 15, 'filled', 'MarkerFaceAlpha', 0.3, 'DisplayName', '预测值');
    hold on; ax = gca;
    lims = [min([ax.XLim(1), ax.YLim(1)]), max([ax.XLim(2), ax.YLim(2)])];
    if all(isfinite(lims)) && lims(1) < lims(2), plot(lims, lims, 'k--', 'LineWidth', 1.5, 'DisplayName', '完美预测'); end
    xlabel(['真实值: ', strrep(label_name, '_', '\_')]);
    ylabel(['预测值: ', strrep(label_name, '_', '\_')]);
    title(sprintf('%s Fold %d 预测 vs 真实 %s', strrep(label_name, '_', '\_'), fold_num, hp_info));
    legend('Location', 'northwest'); grid on; box on; axis equal;
    hp_info_safe = hp_info;
    hp_info_safe = strrep(hp_info_safe, '.', 'p'); hp_info_safe = strrep(hp_info_safe, '(', '');
    hp_info_safe = strrep(hp_info_safe, ')', ''); hp_info_safe = strrep(hp_info_safe, ',', '');
    hp_info_safe = strrep(hp_info_safe, '=', ''); hp_info_safe = strrep(hp_info_safe, ' ', '');
    plot_filename = fullfile(results_dir, sprintf('predicted_vs_actual_%s_fold_%d_%s.png', label_name, fold_num, hp_info_safe));
    saveas(fig, plot_filename); close(fig);
end

function plot_re_distribution(re_data, label_name, results_dir, fold_num, hp_info)
    fig = figure('Visible', 'off');
    histogram(min(re_data, 100), 50, 'FaceAlpha', 0.75);
    xlabel('相对误差 (%)'); ylabel('频率 (计数)');
    title(sprintf('%s Fold %d 相对误差分布 %s', strrep(label_name, '_', '\_'), fold_num, hp_info));
    grid on; box on;
    hp_info_safe = hp_info;
    hp_info_safe = strrep(hp_info_safe, '.', 'p'); hp_info_safe = strrep(hp_info_safe, '(', '');
    hp_info_safe = strrep(hp_info_safe, ')', ''); hp_info_safe = strrep(hp_info_safe, ',', '');
    hp_info_safe = strrep(hp_info_safe, '=', ''); hp_info_safe = strrep(hp_info_safe, ' ', '');
    plot_filename = fullfile(results_dir, sprintf('re_distribution_%s_fold_%d_%s.png', label_name, fold_num, hp_info_safe));
    saveas(fig, plot_filename); close(fig);
end

function plot_aggregated_predictions(true_vals, pred_vals, label_name, final_hp_info, results_dir)
    fig = figure('Visible', 'off', 'Position', [100 100 900 600]);
    scatter(true_vals, pred_vals, 15, 'filled', 'MarkerFaceAlpha', 0.3, 'DisplayName', '聚合预测值');
    hold on; ax = gca; set(ax, 'XScale', 'log', 'YScale', 'log');
    positive_vals = [true_vals(true_vals>0); pred_vals(pred_vals>0)];
    if isempty(positive_vals), lims = [0.01, 1]; else, lims = [min(positive_vals)*0.9, max(positive_vals)*1.1]; end
    if all(isfinite(lims)) && lims(1) < lims(2) && lims(1) > 0, plot(lims, lims, 'k--', 'LineWidth', 1.5, 'DisplayName', '完美预测'); end
    xlabel('真实值 (对数尺度)'); ylabel('预测平均值 (对数尺度)');
    title(sprintf('BNN 聚合预测 vs %s 的真实值 %s', strrep(label_name, '_', '\_'), final_hp_info));
    legend('Location', 'northwest'); grid on; box on;
    final_plot_filename = fullfile(results_dir, sprintf('aggregated_predictions_plot_%s.png', label_name));
    saveas(fig, final_plot_filename);
    fprintf('📈 聚合预测图已保存至 ''%s''\n', final_plot_filename);
    close(fig);
end

function plot_aggregated_re_distribution(re_data, label_name, final_hp_info, results_dir)
    fig = figure('Visible', 'off', 'Position', [100 100 800 500]);
    histogram(min(re_data, 100), 50, 'FaceAlpha', 0.75);
    xlabel('相对误差 (%)'); ylabel('频率 (计数)');
    title(sprintf('%s 聚合相对误差分布 %s', strrep(label_name, '_', '\_'), final_hp_info));
    grid on; box on;
    agg_re_plot_filename = fullfile(results_dir, sprintf('aggregated_re_distribution_%s.png', label_name));
    saveas(fig, agg_re_plot_filename);
    fprintf('📈 聚合相对误差分布图已保存至 ''%s''\n', agg_re_plot_filename);
    close(fig);
end