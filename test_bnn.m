%% test_bnn.m - Simple test for Bayesian Neural Network implementation
% This script tests the BNN implementation with synthetic data

clear; clc; close all;

fprintf('🧪 Testing Bayesian Neural Network Implementation\n');

%% Generate synthetic data
rng(42);
n_samples = 1000;
X_test = randn(n_samples, 4);
y_test = sum(X_test.^2, 2) + 0.1 * randn(n_samples, 1); % Simple quadratic relationship

fprintf('✅ Generated synthetic data: X shape [%d, %d], y shape [%d, %d]\n', ...
    size(X_test, 1), size(X_test, 2), size(y_test, 1), size(y_test, 2));

%% Test Network Architecture
fprintf('\n🔧 Testing Network Architecture...\n');
try
    % Test creating a simple network with dropout layers
    layers = [
        featureInputLayer(4, 'Name', 'input', 'Normalization', 'none')
        fullyConnectedLayer(10, 'Name', 'fc1')
        reluLayer('Name', 'relu1')
        dropoutLayer(0.1, 'Name', 'dropout1')
        fullyConnectedLayer(1, 'Name', 'fc_out')
    ];

    net = dlnetwork(layers);
    fprintf('✅ Network created successfully\n');

    % Test forward pass
    X_dl = dlarray(randn(4, 32), 'CB');
    Y = forward(net, X_dl);
    fprintf('✅ Forward pass successful. Output shape: [%d, %d]\n', size(Y, 1), size(Y, 2));

catch ME
    fprintf('❌ Network architecture test failed: %s\n', ME.message);
    return;
end

%% Test Data File Creation
fprintf('\n📁 Testing Data File Creation...\n');

% Create a small test dataset file
test_data_path = 'test_data.csv';
n_samples_test = 500;
X_synthetic = randn(n_samples_test, 4) * 1e7; % Scale to match expected data range
y_synthetic = [sum(X_synthetic.^2, 2) * 1e-6, sum(X_synthetic.^2, 2) * 1.2e-6]; % Two outputs

% Save test data
test_data = [X_synthetic, y_synthetic];
writematrix(test_data, test_data_path);
fprintf('✅ Test data file created: %s\n', test_data_path);
fprintf('   Data shape: [%d, %d]\n', size(test_data, 1), size(test_data, 2));

fprintf('\n🎉 Basic tests passed successfully!\n');
fprintf('\n✨ The BNN implementation is ready to use!\n');
fprintf('   You can now run BNN_training.m with your actual data.\n');
fprintf('   Or test with the synthetic data by temporarily changing the DATA_PATH in BNN_training.m to ''%s''\n', test_data_path);
