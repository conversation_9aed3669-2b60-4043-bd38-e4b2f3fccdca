classdef bayesFullyConnectedLayer < nnet.layer.Layer & nnet.layer.Formattable
    % Bayesian Fully Connected Layer
    % This layer implements a Bayesian fully connected layer with
    % weight uncertainty using Gaussian distributions
    
    properties
        % Layer properties
        OutputSize
        
        % Prior parameters
        Sigma1
        Sigma2
        MixtureProbability = 0.5
    end
    
    properties (Learnable)
        % Weight parameters (mean and log variance)
        WeightMean
        WeightLogVar
        
        % Bias parameters (mean and log variance)
        BiasMean
        BiasLogVar
        
        % Prior parameters (if learnable)
        PriorSigma1
        PriorSigma2
    end
    
    properties (State)
        % Store KL divergence and log probabilities
        LogPosterior
        LogPrior
    end
    
    methods
        function layer = bayesFullyConnectedLayer(outputSize, varargin)
            % Parse input arguments
            p = inputParser;
            addParameter(p, 'Name', '');
            addParameter(p, 'Sigma1', 1);
            addParameter(p, 'Sigma2', 0.5);
            parse(p, varargin{:});
            
            % Set layer name
            layer.Name = p.Results.Name;
            if isempty(layer.Name)
                layer.Name = sprintf('bayes_fc_%d', outputSize);
            end
            
            % Set layer description
            layer.Description = sprintf('Bayesian FC layer with %d outputs', outputSize);
            
            % Set hyperparameters
            layer.OutputSize = outputSize;
            layer.Sigma1 = p.Results.Sigma1;
            layer.Sigma2 = p.Results.Sigma2;
            
            % Set layer type
            layer.Type = 'Bayesian Fully Connected';
        end
        
        function layer = initialize(layer, layout)
            % Initialize learnable parameters
            
            % Get input size
            % The format is 'CBT' (Channel, Batch, Time) for FC layer
            % The 'C' dimension is the feature dimension.
            inputSize = layout.Size(1);
            outputSize = layer.OutputSize;
            
            % Initialize weight parameters
            % Using Glorot initialization for means
            limit = sqrt(6 / (inputSize + outputSize));
            layer.WeightMean = dlarray(rand(outputSize, inputSize) * 2 * limit - limit);
            layer.WeightLogVar = dlarray(log(0.05) * ones(outputSize, inputSize));
            
            % Initialize bias parameters
            layer.BiasMean = dlarray(zeros(outputSize, 1));
            layer.BiasLogVar = dlarray(log(0.05) * ones(outputSize, 1));
            
            % Initialize prior parameters
            layer.PriorSigma1 = dlarray(layer.Sigma1);
            layer.PriorSigma2 = dlarray(layer.Sigma2);
            
            % Initialize state
            layer.LogPosterior = dlarray(0);
            layer.LogPrior = dlarray(0);
        end
        
        function [Y, state] = forward(layer, X)
            % Forward propagation with weight sampling
            
            % Sample weights from posterior distribution
            % Reparameterization trick
            epsilonW = randn(size(layer.WeightMean), 'like', layer.WeightMean);
            weight = layer.WeightMean + exp(0.5 * layer.WeightLogVar) .* epsilonW;
            
            % Sample biases from posterior distribution
            % Reparameterization trick
            epsilonB = randn(size(layer.BiasMean), 'like', layer.BiasMean);
            bias = layer.BiasMean + exp(0.5 * layer.BiasLogVar) .* epsilonB;
            
            % Compute output
            Y = weight * X + bias;
            
            % Calculate log probabilities for KL divergence
            % Log posterior q(w|theta)
            logPosteriorW = logGaussian(weight, layer.WeightMean, exp(layer.WeightLogVar));
            logPosteriorB = logGaussian(bias, layer.BiasMean, exp(layer.BiasLogVar));
            layer.LogPosterior = sum(logPosteriorW, 'all') + sum(logPosteriorB, 'all');
            
            % Log prior p(w) (Gaussian mixture)
            logPriorW = logGaussianMixture(weight, 0, layer.PriorSigma1, layer.PriorSigma2, layer.MixtureProbability);
            logPriorB = logGaussianMixture(bias, 0, layer.PriorSigma1, layer.PriorSigma2, layer.MixtureProbability);
            layer.LogPrior = sum(logPriorW, 'all') + sum(logPriorB, 'all');
            
            % Return state
            state.LogPosterior = layer.LogPosterior;
            state.LogPrior = layer.LogPrior;
        end
    end
end

% Helper functions
function logP = logGaussian(x, mu, sigma2)
    % Log probability of Gaussian distribution
    logP = -0.5 * ((x - mu).^2 ./ sigma2 + log(2 * pi * sigma2));
end

function logP = logGaussianMixture(x, mu, sigma1, sigma2, pi)
    % Log probability of Gaussian mixture
    logP1 = logGaussian(x, mu, sigma1^2);
    logP2 = logGaussian(x, mu, sigma2^2);
    
    % Use log-sum-exp trick for numerical stability
    maxLogP = max(logP1, logP2);
    logP = maxLogP + log(pi * exp(logP1 - maxLogP) + (1 - pi) * exp(logP2 - maxLogP));
end